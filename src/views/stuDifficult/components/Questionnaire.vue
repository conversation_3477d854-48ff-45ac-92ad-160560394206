<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 15:59:15
-->
<template>
  <div class="p-10px" style="max-width: 600px; margin: 0 auto">
    <Mtcn-Alert v-if="props.opType === 1" showIcon message="必须答完所有题目才能保存哦，请勿中途退出！"> </Mtcn-Alert>
    <BasicForm @register="registerForm" class="m-t-10px"> </BasicForm>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const props = defineProps({
    opType: {
      type: Number,
      default: 1, //1 学生 2 管理员
    },
    operationType: {
      type: Number,
      default: -1,
    },
  });
  const emit = defineEmits(['register', 'reload']);
  const id = ref('');
  const baseStore = useBaseStore();
  const api = useBaseApi('/api/knsPdxx');

  const { createMessage } = useMessage();
  const formArr = ref([]);
  const familyMembersForm = ref({});
  const formRefs = ref({});
  const data = ref([]);
  const readonly = ref(false);

  const schemas = ref([]);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: schemas.value,
    layout: 'vertical',
    labelWidth: '100%',
  });

  async function initForm() {
    const res = await api.request('get', '/api/knsDcwj/getDcwjList', { isFullPath: true });
    debugger;
    data.value = res.data || [];
    for (const item of data.value) {
      let obj = {};
      if (item.tmlx == 1 || item.tmlx == 3) {
        obj = {
          options: item.xxList.map((item) => {
            return {
              fullName: item.xxmc,
              enCode: item.xxdm,
              tmdm: item.tmdm,
              fz: item.fz,
            },
          }),
          fieldNames: { label: 'fullName', value: 'enCode' },
        };
      }
      addSchema(
        `${item.wjdm}_${item.tmdm}_${item.xxdm}_${item.qtda}`,
        item.tmmc,
        item.tmtxfslx || 'Input',
        item.sfbt || false,
        { sm: 8 },
        { placeholder: item.tmlx == 2?'请输入':'请选择', ...obj, disabled: props.operationType != -1 || readonly.value },
      );
    }
  }

  function addSchema(field, label, component, required = '', colProps, componentProps = {}, slot) {
    schemas.value.push({
      field,
      label,
      component,
      required: required == '1',
      colProps: { sm: 24, ...colProps },
      componentProps,
      slot,
    });
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    return values;
  }



  onMounted(() => {
    initForm();
    // if (props.opType === 2) {
    //   const updates = schemas.value.map((item, index) => ({
    //     field: item.field,
    //     componentProps: {
    //       ...item.componentProps,
    //       disabled: index !== 0,
    //     },
    //   }));
    //   updateSchema(updates);
    // }
  });

    defineExpose({
    handleSubmit,
  });
</script>
